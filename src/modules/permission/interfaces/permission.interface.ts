/**
 * Structure of permissions stored in Package entity's permissions JSONB column
 */
export interface PackagePermissions {
  /**
   * List of features available to this package
   * e.g., ['basic_worksheets', 'exam_creation', 'student_management']
   */
  features?: string[];

  /**
   * Numeric limits for various resources
   * e.g., { maxStudents: 100, maxWorksheets: 50, maxExams: 25 }
   */
  limits?: Record<string, number>;

  /**
   * Boolean access permissions for specific functionality
   * e.g., { adminPanel: true, analytics: false, advancedReports: false }
   */
  access?: Record<string, boolean>;
}

/**
 * User's effective permissions based on their subscription package
 */
export interface UserPermissions {
  /**
   * List of features available to the user
   */
  features: string[];

  /**
   * Numeric limits for various resources
   */
  limits: Record<string, number>;

  /**
   * Boolean access permissions for specific functionality
   */
  access: Record<string, boolean>;

  /**
   * Name of the package providing these permissions
   */
  packageName?: string;

  /**
   * Current subscription status
   */
  subscriptionStatus?: string;

  /**
   * Whether user has an active subscription
   */
  hasActiveSubscription: boolean;
}

/**
 * Default permissions for users without active subscriptions
 */
export const DEFAULT_PERMISSIONS: UserPermissions = {
  features: ['basic_worksheets'],
  limits: {
    maxStudents: 5,
    maxWorksheets: 3,
    maxExams: 1,
    maxDailyQuestions: 20,
  },
  access: {
    adminPanel: false,
    analytics: false,
    advancedReports: false,
  },
  hasActiveSubscription: false,
};
