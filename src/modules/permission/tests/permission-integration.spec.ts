import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import request from 'supertest';
import { PermissionModule } from '../permission.module';
import { PermissionService } from '../permission.service';
import { UsageTrackingService } from '../../usage-tracking/services/usage-tracking.service';
import { SubscriptionService } from '../../subscription/subscription.service';
import { RedisService } from '../../redis/redis.service';
import { AuthModule } from '../../auth/auth.module';
import { WorksheetModule } from '../../worksheet/worksheet.module';
import { ExamModule } from '../../exam/exam.module';
import { UserPermissions } from '../interfaces/permission.interface';

describe('Permission Integration Tests', () => {
  let app: INestApplication;
  let permissionService: jest.Mocked<PermissionService>;
  let usageTrackingService: jest.Mocked<UsageTrackingService>;
  let subscriptionService: jest.Mocked<SubscriptionService>;
  let redisService: jest.Mocked<RedisService>;

  // Mock user contexts for different subscription levels
  const basicUser = {
    sub: 'basic-user-123',
    email: '<EMAIL>',
    role: 'TEACHER',
    schoolId: 'school-123'
  };

  const premiumUser = {
    sub: 'premium-user-123',
    email: '<EMAIL>',
    role: 'TEACHER',
    schoolId: 'school-123'
  };

  const adminUser = {
    sub: 'admin-user-123',
    email: '<EMAIL>',
    role: 'ADMIN',
    schoolId: 'school-123'
  };

  // Mock permissions for different subscription levels
  const basicPermissions: UserPermissions = {
    features: ['basic_worksheets'],
    limits: {
      maxStudents: 5,
      maxWorksheets: 3,
      maxExams: 1,
    },
    access: {
      adminPanel: false,
      analytics: false,
      advancedReports: false,
    },
    hasActiveSubscription: true,
    packageName: 'Basic Package',
    subscriptionStatus: 'active'
  };

  const premiumPermissions: UserPermissions = {
    features: ['basic_worksheets', 'exam_creation', 'student_management'],
    limits: {
      maxStudents: 100,
      maxWorksheets: 50,
      maxExams: 25,
    },
    access: {
      adminPanel: false,
      analytics: true,
      advancedReports: true,
    },
    hasActiveSubscription: true,
    packageName: 'Premium Package',
    subscriptionStatus: 'active'
  };

  const adminPermissions: UserPermissions = {
    features: ['basic_worksheets', 'exam_creation', 'student_management'],
    limits: {
      maxStudents: 1000,
      maxWorksheets: 500,
      maxExams: 100,
    },
    access: {
      adminPanel: true,
      analytics: true,
      advancedReports: true,
    },
    hasActiveSubscription: true,
    packageName: 'Admin Package',
    subscriptionStatus: 'active'
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        // Mock TypeORM for testing
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [],
          synchronize: true,
        }),
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        AuthModule,
        PermissionModule,
        WorksheetModule,
        ExamModule,
      ],
    })
    .overrideProvider(PermissionService)
    .useValue({
      getUserPermissions: jest.fn(),
      hasFeature: jest.fn(),
      hasAccess: jest.fn(),
      getLimit: jest.fn(),
    })
    .overrideProvider(UsageTrackingService)
    .useValue({
      checkUsageLimit: jest.fn(),
      incrementUsage: jest.fn(),
      getCurrentUsage: jest.fn(),
    })
    .overrideProvider(SubscriptionService)
    .useValue({
      findByUserId: jest.fn(),
    })
    .overrideProvider(RedisService)
    .useValue({
      getClient: jest.fn().mockReturnValue({
        get: jest.fn(),
        set: jest.fn(),
        incrby: jest.fn(),
        expire: jest.fn(),
      }),
    })
    .compile();

    app = moduleFixture.createNestApplication();
    permissionService = moduleFixture.get(PermissionService);
    usageTrackingService = moduleFixture.get(UsageTrackingService);
    subscriptionService = moduleFixture.get(SubscriptionService);
    redisService = moduleFixture.get(RedisService);

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('Worksheet Permission Integration', () => {
    it('should allow premium user to create worksheet when within limits', async () => {
      // Mock JWT token validation
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(premiumUser);
      
      // Mock permission checks
      permissionService.hasFeature.mockResolvedValue(true);
      usageTrackingService.checkUsageLimit.mockResolvedValue({
        withinLimit: true,
        current: 5,
        limit: 50,
        remaining: 45,
        period: 'daily',
        feature: 'maxWorksheets'
      });

      const response = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', 'Bearer valid-token')
        .send({
          title: 'Test Worksheet',
          description: 'Test Description',
          schoolId: 'school-123'
        });

      expect(response.status).toBe(201);
      expect(permissionService.hasFeature).toHaveBeenCalledWith('premium-user-123', 'basic_worksheets');
      expect(usageTrackingService.checkUsageLimit).toHaveBeenCalledWith('premium-user-123', 'maxWorksheets', 'daily');
    });

    it('should deny basic user access when feature not available', async () => {
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(basicUser);
      
      // Mock permission checks - user doesn't have exam_creation feature
      permissionService.hasFeature.mockResolvedValue(false);

      const response = await request(app.getHttpServer())
        .post('/exams')
        .set('Authorization', 'Bearer valid-token')
        .send({
          worksheetId: 'worksheet-123',
          title: 'Test Exam'
        });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Feature not available');
    });

    it('should deny user when usage limit exceeded', async () => {
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(basicUser);
      
      // Mock permission checks - user has feature but exceeded limit
      permissionService.hasFeature.mockResolvedValue(true);
      usageTrackingService.checkUsageLimit.mockResolvedValue({
        withinLimit: false,
        current: 3,
        limit: 3,
        remaining: 0,
        period: 'daily',
        feature: 'maxWorksheets'
      });

      const response = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', 'Bearer valid-token')
        .send({
          title: 'Test Worksheet',
          description: 'Test Description',
          schoolId: 'school-123'
        });

      expect(response.status).toBe(429);
      expect(response.body.message).toContain('Usage limit exceeded');
    });
  });

  describe('Exam Permission Integration', () => {
    it('should allow premium user to create exam when within limits', async () => {
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(premiumUser);
      
      permissionService.hasFeature.mockResolvedValue(true);
      usageTrackingService.checkUsageLimit.mockResolvedValue({
        withinLimit: true,
        current: 5,
        limit: 25,
        remaining: 20,
        period: 'weekly',
        feature: 'maxExams'
      });

      const response = await request(app.getHttpServer())
        .post('/exams')
        .set('Authorization', 'Bearer valid-token')
        .send({
          worksheetId: 'worksheet-123',
          title: 'Test Exam'
        });

      expect(response.status).toBe(201);
      expect(permissionService.hasFeature).toHaveBeenCalledWith('premium-user-123', 'exam_creation');
      expect(usageTrackingService.checkUsageLimit).toHaveBeenCalledWith('premium-user-123', 'maxExams', 'weekly');
    });

    it('should allow premium user to get exam when has feature', async () => {
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(premiumUser);
      
      permissionService.hasFeature.mockResolvedValue(true);

      const response = await request(app.getHttpServer())
        .get('/exams/exam-123')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(200);
      expect(permissionService.hasFeature).toHaveBeenCalledWith('premium-user-123', 'exam_creation');
    });
  });

  describe('Admin Access Integration', () => {
    it('should allow admin to access sync endpoint', async () => {
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(adminUser);
      
      permissionService.hasAccess.mockResolvedValue(true);

      const response = await request(app.getHttpServer())
        .post('/worksheets/worksheet-123/sync')
        .set('Authorization', 'Bearer valid-token')
        .query({ dryRun: true });

      expect(response.status).toBe(200);
      expect(permissionService.hasAccess).toHaveBeenCalledWith('admin-user-123', 'adminPanel');
    });

    it('should deny non-admin access to sync endpoint', async () => {
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(basicUser);
      
      permissionService.hasAccess.mockResolvedValue(false);

      const response = await request(app.getHttpServer())
        .post('/worksheets/worksheet-123/sync')
        .set('Authorization', 'Bearer valid-token')
        .query({ dryRun: true });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });
  });

  describe('Combined Permission Checks', () => {
    it('should handle multiple permission requirements', async () => {
      jest.spyOn(app.get('JwtService'), 'verifyAsync').mockResolvedValue(premiumUser);
      
      // Mock all required checks passing
      permissionService.hasFeature.mockResolvedValue(true);
      permissionService.hasAccess.mockResolvedValue(true);
      usageTrackingService.checkUsageLimit.mockResolvedValue({
        withinLimit: true,
        current: 5,
        limit: 25,
        remaining: 20,
        period: 'weekly',
        feature: 'maxExams'
      });

      // This would be an endpoint that requires feature + access + usage check
      // Using the permission example controller endpoint
      const response = await request(app.getHttpServer())
        .post('/permission-examples/advanced-exam')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(200);
    });
  });
});
