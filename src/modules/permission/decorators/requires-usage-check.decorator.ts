import { SetMetadata } from '@nestjs/common';
import { UsagePeriod } from '../../usage-tracking/interfaces/usage-tracking.interface';

/**
 * Metadata key for required usage checks
 */
export const REQUIRED_USAGE_CHECK_KEY = 'requiredUsageCheck';

/**
 * Interface for usage check configuration
 */
export interface UsageCheckConfig {
  /**
   * The feature to check usage for (e.g., 'maxWorksheets', 'maxStudents')
   */
  feature: string;
  
  /**
   * The time period for the usage check (default: 'daily')
   */
  period?: UsagePeriod;
}

/**
 * Decorator to specify required usage limit checks for accessing an endpoint
 * This decorator works with the PermissionGuard to enforce usage limits
 * 
 * @param feature - The feature name to check usage for
 * @param period - The time period for the check (default: 'daily')
 * 
 * @example
 * ```typescript
 * @RequiresUsageCheck('maxWorksheets')
 * async createWorksheet() {
 *   // Only users within their worksheet limit can access
 * }
 * 
 * @RequiresUsageCheck('maxStudents', 'monthly')
 * async addStudent() {
 *   // Check monthly student limit
 * }
 * ```
 */
export const RequiresUsageCheck = (feature: string, period: UsagePeriod = 'daily') => {
  const config: UsageCheckConfig = { feature, period };
  return SetMetadata(REQUIRED_USAGE_CHECK_KEY, config);
};
