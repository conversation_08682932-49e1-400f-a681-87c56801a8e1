import { Test, TestingModule } from '@nestjs/testing';
import { UsageTrackingService } from './usage-tracking.service';
import { RedisService } from '../../redis/redis.service';
import { PermissionService } from '../../permission/permission.service';

describe('UsageTrackingService', () => {
  let service: UsageTrackingService;
  let redisService: jest.Mocked<RedisService>;
  let permissionService: jest.Mocked<PermissionService>;
  let mockRedisClient: any;

  beforeEach(async () => {
    // Mock Redis client
    mockRedisClient = {
      incrby: jest.fn(),
      expire: jest.fn(),
      get: jest.fn(),
      del: jest.fn(),
    };

    // Mock RedisService
    const mockRedisService = {
      getClient: jest.fn().mockReturnValue(mockRedisClient),
    };

    // Mock PermissionService
    const mockPermissionService = {
      getLimit: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsageTrackingService,
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: PermissionService,
          useValue: mockPermissionService,
        },
      ],
    }).compile();

    service = module.get<UsageTrackingService>(UsageTrackingService);
    redisService = module.get(RedisService);
    permissionService = module.get(PermissionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('incrementUsage', () => {
    it('should increment usage and set TTL for new key', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';
      const amount = 1;

      mockRedisClient.incrby.mockResolvedValue(1); // First increment

      const result = await service.incrementUsage(userId, feature, amount);

      expect(mockRedisClient.incrby).toHaveBeenCalledWith(
        expect.stringContaining(`usage:daily:`),
        amount,
      );
      expect(mockRedisClient.expire).toHaveBeenCalledWith(
        expect.stringContaining(`usage:daily:`),
        2 * 24 * 60 * 60, // 2 days TTL
      );
      expect(result).toBe(1);
    });

    it('should increment usage without setting TTL for existing key', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';
      const amount = 1;

      mockRedisClient.incrby.mockResolvedValue(5); // Existing key

      const result = await service.incrementUsage(userId, feature, amount);

      expect(mockRedisClient.incrby).toHaveBeenCalled();
      expect(mockRedisClient.expire).not.toHaveBeenCalled();
      expect(result).toBe(5);
    });
  });

  describe('getCurrentUsage', () => {
    it('should return current usage count', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockResolvedValue('3');

      const result = await service.getCurrentUsage(userId, feature);

      expect(mockRedisClient.get).toHaveBeenCalledWith(
        expect.stringContaining(`usage:daily:`),
      );
      expect(result).toBe(3);
    });

    it('should return 0 when no usage exists', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockResolvedValue(null);

      const result = await service.getCurrentUsage(userId, feature);

      expect(result).toBe(0);
    });

    it('should return 0 on Redis error', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockRejectedValue(new Error('Redis error'));

      const result = await service.getCurrentUsage(userId, feature);

      expect(result).toBe(0);
    });
  });

  describe('checkUsageLimit', () => {
    it('should return within limit when usage is below limit', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockResolvedValue('3');
      permissionService.getLimit.mockResolvedValue(10);

      const result = await service.checkUsageLimit(userId, feature);

      expect(result).toEqual({
        withinLimit: true,
        current: 3,
        limit: 10,
        remaining: 7,
        period: 'daily',
        feature: 'maxWorksheets',
      });
    });

    it('should return over limit when usage equals or exceeds limit', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockResolvedValue('10');
      permissionService.getLimit.mockResolvedValue(10);

      const result = await service.checkUsageLimit(userId, feature);

      expect(result).toEqual({
        withinLimit: false,
        current: 10,
        limit: 10,
        remaining: 0,
        period: 'daily',
        feature: 'maxWorksheets',
      });
    });

    it('should deny access when no limit is defined', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockResolvedValue('3');
      permissionService.getLimit.mockResolvedValue(null);

      const result = await service.checkUsageLimit(userId, feature);

      expect(result).toEqual({
        withinLimit: false,
        current: 3,
        limit: 0,
        remaining: 0,
        period: 'daily',
        feature: 'maxWorksheets',
      });
    });
  });

  describe('isUsageLimitReached', () => {
    it('should return true when limit is reached', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockResolvedValue('10');
      permissionService.getLimit.mockResolvedValue(10);

      const result = await service.isUsageLimitReached(userId, feature);

      expect(result).toBe(true);
    });

    it('should return false when within limit', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.get.mockResolvedValue('5');
      permissionService.getLimit.mockResolvedValue(10);

      const result = await service.isUsageLimitReached(userId, feature);

      expect(result).toBe(false);
    });
  });

  describe('resetUsage', () => {
    it('should reset usage successfully', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.del.mockResolvedValue(1);

      const result = await service.resetUsage(userId, feature);

      expect(mockRedisClient.del).toHaveBeenCalledWith(
        expect.stringContaining(`usage:daily:`),
      );
      expect(result).toBe(true);
    });

    it('should return false when key does not exist', async () => {
      const userId = 'user-123';
      const feature = 'maxWorksheets';

      mockRedisClient.del.mockResolvedValue(0);

      const result = await service.resetUsage(userId, feature);

      expect(result).toBe(false);
    });
  });
});
