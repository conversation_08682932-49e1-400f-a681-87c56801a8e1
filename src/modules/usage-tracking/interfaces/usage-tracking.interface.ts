/**
 * Supported time periods for usage tracking
 */
export type UsagePeriod = 'daily' | 'weekly' | 'monthly';

/**
 * Usage limit check result
 */
export interface UsageLimitCheckResult {
  /**
   * Whether the user is within the usage limit
   */
  withinLimit: boolean;

  /**
   * Current usage count
   */
  current: number;

  /**
   * Maximum allowed usage (limit)
   */
  limit: number;

  /**
   * Remaining usage before hitting the limit
   */
  remaining: number;

  /**
   * The period this check applies to
   */
  period: UsagePeriod;

  /**
   * The feature being checked
   */
  feature: string;
}

/**
 * Usage tracking configuration
 */
export interface UsageTrackingConfig {
  /**
   * TTL for different periods (in seconds)
   */
  ttl: {
    daily: number;
    weekly: number;
    monthly: number;
  };

  /**
   * Redis key prefixes
   */
  keyPrefixes: {
    daily: string;
    weekly: string;
    monthly: string;
  };
}

/**
 * Default configuration for usage tracking
 */
export const DEFAULT_USAGE_TRACKING_CONFIG: UsageTrackingConfig = {
  ttl: {
    daily: 2 * 24 * 60 * 60, // 2 days
    weekly: 8 * 24 * 60 * 60, // 8 days
    monthly: 32 * 24 * 60 * 60, // 32 days
  },
  keyPrefixes: {
    daily: 'usage:daily:',
    weekly: 'usage:weekly:',
    monthly: 'usage:monthly:',
  },
};
