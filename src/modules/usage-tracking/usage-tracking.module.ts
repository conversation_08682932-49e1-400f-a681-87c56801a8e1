import { Module, forwardRef } from '@nestjs/common';
import { UsageTrackingService } from './services/usage-tracking.service';
import { PermissionModule } from '../permission/permission.module';

@Module({
  imports: [
    // Import PermissionModule to access PermissionService
    forwardRef(() => PermissionModule),
    // RedisModule is already global, so no need to import
  ],
  providers: [UsageTrackingService],
  exports: [UsageTrackingService],
})
export class UsageTrackingModule {}
