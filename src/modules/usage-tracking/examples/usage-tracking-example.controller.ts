import { Controller, Post, Get, Param, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { ActiveUser } from '../../auth/decorators/active-user.decorator';
import { ActiveUserData } from '../../auth/decorators/active-user.decorator';
import { UsageTrackingService } from '../services/usage-tracking.service';
import { UsageLimitCheckResult } from '../interfaces/usage-tracking.interface';

/**
 * Example controller demonstrating how to use UsageTrackingService
 * This is for demonstration purposes only
 */
@ApiTags('Usage Tracking Examples')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('examples/usage-tracking')
export class UsageTrackingExampleController {
  private readonly logger = new Logger(UsageTrackingExampleController.name);

  constructor(private readonly usageTrackingService: UsageTrackingService) {}

  /**
   * Example: Increment worksheet creation usage
   */
  @Post('increment-worksheet-usage')
  @ApiOperation({ 
    summary: 'Increment worksheet creation usage',
    description: 'Example of how to track worksheet creation usage'
  })
  @ApiResponse({ status: 200, description: 'Usage incremented successfully' })
  async incrementWorksheetUsage(@ActiveUser() user: ActiveUserData) {
    try {
      // Increment daily worksheet usage by 1
      const newCount = await this.usageTrackingService.incrementUsage(
        user.sub,
        'maxWorksheets',
        1,
        'daily'
      );

      this.logger.log(`User ${user.sub} worksheet usage incremented to: ${newCount}`);

      return {
        success: true,
        message: 'Worksheet usage incremented',
        data: {
          userId: user.sub,
          feature: 'maxWorksheets',
          newCount,
          period: 'daily'
        }
      };
    } catch (error) {
      this.logger.error(`Error incrementing worksheet usage: ${error.message}`);
      throw error;
    }
  }

  /**
   * Example: Check current worksheet usage
   */
  @Get('current-worksheet-usage')
  @ApiOperation({ 
    summary: 'Get current worksheet usage',
    description: 'Example of how to check current usage count'
  })
  @ApiResponse({ status: 200, description: 'Current usage retrieved' })
  async getCurrentWorksheetUsage(@ActiveUser() user: ActiveUserData) {
    try {
      const currentUsage = await this.usageTrackingService.getCurrentUsage(
        user.sub,
        'maxWorksheets',
        'daily'
      );

      return {
        success: true,
        data: {
          userId: user.sub,
          feature: 'maxWorksheets',
          currentUsage,
          period: 'daily'
        }
      };
    } catch (error) {
      this.logger.error(`Error getting current worksheet usage: ${error.message}`);
      throw error;
    }
  }

  /**
   * Example: Check if user can create more worksheets
   */
  @Get('check-worksheet-limit')
  @ApiOperation({ 
    summary: 'Check worksheet creation limit',
    description: 'Example of how to check if user can create more worksheets'
  })
  @ApiResponse({ status: 200, description: 'Limit check completed' })
  async checkWorksheetLimit(@ActiveUser() user: ActiveUserData): Promise<{
    success: boolean;
    data: UsageLimitCheckResult;
  }> {
    try {
      const limitCheck = await this.usageTrackingService.checkUsageLimit(
        user.sub,
        'maxWorksheets',
        'daily'
      );

      return {
        success: true,
        data: limitCheck
      };
    } catch (error) {
      this.logger.error(`Error checking worksheet limit: ${error.message}`);
      throw error;
    }
  }

  /**
   * Example: Check if worksheet limit is reached (simple boolean)
   */
  @Get('is-worksheet-limit-reached')
  @ApiOperation({ 
    summary: 'Check if worksheet limit is reached',
    description: 'Example of simple boolean check for limit reached'
  })
  @ApiResponse({ status: 200, description: 'Limit reached check completed' })
  async isWorksheetLimitReached(@ActiveUser() user: ActiveUserData) {
    try {
      const limitReached = await this.usageTrackingService.isUsageLimitReached(
        user.sub,
        'maxWorksheets',
        'daily'
      );

      return {
        success: true,
        data: {
          userId: user.sub,
          feature: 'maxWorksheets',
          limitReached,
          period: 'daily'
        }
      };
    } catch (error) {
      this.logger.error(`Error checking if worksheet limit reached: ${error.message}`);
      throw error;
    }
  }

  /**
   * Example: Check usage for different periods
   */
  @Get('usage-summary')
  @ApiOperation({ 
    summary: 'Get usage summary for different periods',
    description: 'Example of checking usage across daily, weekly, and monthly periods'
  })
  @ApiResponse({ status: 200, description: 'Usage summary retrieved' })
  async getUsageSummary(@ActiveUser() user: ActiveUserData) {
    try {
      const [dailyUsage, weeklyUsage, monthlyUsage] = await Promise.all([
        this.usageTrackingService.getCurrentUsage(user.sub, 'maxWorksheets', 'daily'),
        this.usageTrackingService.getCurrentUsage(user.sub, 'maxWorksheets', 'weekly'),
        this.usageTrackingService.getCurrentUsage(user.sub, 'maxWorksheets', 'monthly'),
      ]);

      const [dailyLimit, weeklyLimit, monthlyLimit] = await Promise.all([
        this.usageTrackingService.checkUsageLimit(user.sub, 'maxWorksheets', 'daily'),
        this.usageTrackingService.checkUsageLimit(user.sub, 'maxWorksheets', 'weekly'),
        this.usageTrackingService.checkUsageLimit(user.sub, 'maxWorksheets', 'monthly'),
      ]);

      return {
        success: true,
        data: {
          userId: user.sub,
          feature: 'maxWorksheets',
          summary: {
            daily: {
              usage: dailyUsage,
              limit: dailyLimit.limit,
              remaining: dailyLimit.remaining,
              withinLimit: dailyLimit.withinLimit
            },
            weekly: {
              usage: weeklyUsage,
              limit: weeklyLimit.limit,
              remaining: weeklyLimit.remaining,
              withinLimit: weeklyLimit.withinLimit
            },
            monthly: {
              usage: monthlyUsage,
              limit: monthlyLimit.limit,
              remaining: monthlyLimit.remaining,
              withinLimit: monthlyLimit.withinLimit
            }
          }
        }
      };
    } catch (error) {
      this.logger.error(`Error getting usage summary: ${error.message}`);
      throw error;
    }
  }

  /**
   * Example: Reset usage (admin operation)
   */
  @Post('reset-worksheet-usage/:period')
  @ApiOperation({ 
    summary: 'Reset worksheet usage for a period',
    description: 'Example of how to reset usage (typically for admin/testing purposes)'
  })
  @ApiResponse({ status: 200, description: 'Usage reset completed' })
  async resetWorksheetUsage(
    @ActiveUser() user: ActiveUserData,
    @Param('period') period: 'daily' | 'weekly' | 'monthly'
  ) {
    try {
      const resetSuccess = await this.usageTrackingService.resetUsage(
        user.sub,
        'maxWorksheets',
        period
      );

      return {
        success: true,
        data: {
          userId: user.sub,
          feature: 'maxWorksheets',
          period,
          resetSuccess
        }
      };
    } catch (error) {
      this.logger.error(`Error resetting worksheet usage: ${error.message}`);
      throw error;
    }
  }
}
